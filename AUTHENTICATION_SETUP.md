# Authentication Setup Guide

This guide will help you set up Google authentication with Supabase for the Lavvel platform.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. A Google Cloud Console project for OAuth

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization and enter project details
4. Wait for the project to be created

## Step 2: Configure Google OAuth in Supabase

1. In your Supabase dashboard, go to **Authentication** > **Providers**
2. Find **Google** in the list and click to configure
3. Enable the Google provider
4. You'll need to set up Google OAuth credentials (see Step 3)

## Step 3: Set up Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to **APIs & Services** > **Library**
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to **APIs & Services** > **Credentials**
   - Click **Create Credentials** > **OAuth 2.0 Client IDs**
   - Choose **Web application**
   - Add your authorized redirect URIs:
     - For development: `http://localhost:5173`
     - For production: `https://your-domain.com`
     - Supabase callback: `https://your-project-id.supabase.co/auth/v1/callback`
5. Copy the **Client ID** and **Client Secret**

## Step 4: Configure Supabase with Google Credentials

1. Back in Supabase, in the Google provider configuration:
2. Enter your Google **Client ID**
3. Enter your Google **Client Secret**
4. Save the configuration

## Step 5: Set up Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. Find these values in your Supabase dashboard:
   - Go to **Settings** > **API**
   - Copy the **Project URL** and **anon/public key**

## Step 6: Test the Authentication

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open the application in your browser
3. Click "Get Started, It's Free!" button
4. The sign-in dialog should appear
5. Click "Continue with Google" to test the authentication flow

## Features Implemented

- **Google OAuth Sign-in**: Users can sign in with their Google account
- **User Profile Display**: Authenticated users see their profile in the top-right corner
- **Authentication Guard**: The "Get Started" button shows sign-in dialog for unauthenticated users
- **Session Persistence**: User sessions are maintained across browser refreshes
- **Sign Out**: Users can sign out from the profile dropdown

## File Structure

```
src/
├── components/
│   └── auth/
│       ├── SignInDialog.vue      # Google sign-in dialog
│       ├── UserProfile.vue       # User profile dropdown
│       └── index.ts              # Auth components exports
├── composables/
│   └── useAuth.ts                # Authentication composable
├── lib/
│   └── supabase.ts               # Supabase client configuration
└── stores/
    └── auth.ts                   # Authentication state management
```

## Troubleshooting

### Common Issues

1. **"Missing Supabase environment variables" error**
   - Make sure your `.env` file exists and has the correct values
   - Restart the development server after updating `.env`

2. **Google OAuth not working**
   - Check that your Google OAuth credentials are correctly configured
   - Verify the redirect URIs in Google Cloud Console
   - Ensure the Google+ API is enabled

3. **User not persisting after refresh**
   - Check browser console for any Supabase errors
   - Verify your Supabase project is active and not paused

### Getting Help

- Check the [Supabase documentation](https://supabase.com/docs/guides/auth/social-login/auth-google)
- Review the [Google OAuth documentation](https://developers.google.com/identity/protocols/oauth2)
- Open an issue in the project repository if you encounter bugs
