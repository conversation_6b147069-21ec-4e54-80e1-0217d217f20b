{"name": "lavvel", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@types/blob-stream": "^0.1.33", "@types/papaparse": "^5.3.14", "@types/qrcode": "^1.5.5", "@types/svg-to-pdfkit": "^0.1.3", "@vueuse/core": "^11.0.3", "blob-stream": "^0.1.3", "buffer": "^6.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "lucide-vue-next": "^0.441.0", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pinia": "^2.1.7", "qrcode": "^1.5.4", "radix-vue": "^1.9.5", "svg-to-pdfkit": "^0.1.8", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vue": "^3.4.29", "vue-pdf-printer": "^0.0.1", "vue-router": "^4.3.3"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.25", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/chrome": "^0.0.271", "@types/node": "^20.16.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.45", "prettier": "^3.2.5", "tailwindcss": "^3.4.11", "typescript": "~5.4.0", "vite": "^5.4.5", "vue-tsc": "^2.0.21"}}