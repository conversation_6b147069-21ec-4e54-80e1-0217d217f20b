<template>
  <Dialog :open="isOpen" @update:open="handleDialogOpen">
    <DialogTrigger as-child>
      <slot />
    </DialogTrigger>
    <DialogContent class="sm:max-w-[400px]">
      <DialogHeader>
        <DialogTitle class="text-center">Welcome to Lavvel</DialogTitle>
        <DialogDescription class="text-center">
          Sign in to access all features and save your progress
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- Google Sign In Button -->
        <Button @click="handleGoogleSignIn" :disabled="loading" class="w-full h-12 text-base" variant="outline">
          <div class="flex items-center justify-center gap-3">
            <svg v-if="!loading" class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
              <path fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
              <path fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
              <path fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
            </svg>
            <Loader2 v-else class="w-5 h-5 animate-spin" />
            <span>{{ loading ? 'Signing in...' : 'Continue with Google' }}</span>
          </div>
        </Button>

        <!-- Error Message -->
        <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd" />
            </svg>
            <span class="text-sm text-red-700">{{ error }}</span>
          </div>
        </div>

        <!-- Privacy Notice -->
        <div class="text-xs text-muted-foreground text-center space-y-1">
          <p>By signing in, you agree to our Terms of Service and Privacy Policy.</p>
          <p>We'll only access your basic profile information.</p>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Dialog, DialogTitle, DialogDescription, DialogTrigger, DialogContent, DialogHeader } from '@/components/ui/dialog'
import { Loader2 } from 'lucide-vue-next'
import { useAuth } from '@/composables/useAuth'

// Props
interface Props {
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  open: false
})

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  'success': []
}>()

// Auth composable
const { signInWithGoogle, loading, error, clearError } = useAuth()

// Local state
const isOpen = ref(props.open)

// Handlers
const handleDialogOpen = (state: boolean) => {
  isOpen.value = state
  emit('update:open', state)

  if (!state) {
    clearError()
  }
}

const handleGoogleSignIn = async () => {
  const result = await signInWithGoogle()

  if (!result.error) {
    // Success - close dialog and emit success
    handleDialogOpen(false)
    emit('success')
  }
  // Error is handled by the auth store and displayed in the UI
}

// Watch for prop changes
watch(() => props.open, (newValue) => {
  isOpen.value = newValue
})
</script>
