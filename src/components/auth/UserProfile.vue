<template>
  <div class="relative">
    <!-- User Avatar/Button -->
    <Button
      @click="toggleDropdown"
      variant="ghost"
      size="sm"
      class="h-10 w-10 rounded-full p-0 hover:bg-white/10"
    >
      <img
        v-if="userAvatar"
        :src="userAvatar"
        :alt="userDisplayName || 'User'"
        class="h-8 w-8 rounded-full object-cover"
      />
      <div
        v-else
        class="h-8 w-8 rounded-full bg-white/20 flex items-center justify-center text-white text-sm font-medium"
      >
        {{ getInitials(userDisplayName || 'U') }}
      </div>
    </Button>

    <!-- Dropdown Menu -->
    <div
      v-if="isDropdownOpen"
      class="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
      @click.stop
    >
      <!-- User Info -->
      <div class="px-4 py-3 border-b border-gray-100">
        <div class="flex items-center gap-3">
          <img
            v-if="userAvatar"
            :src="userAvatar"
            :alt="userDisplayName || 'User'"
            class="h-10 w-10 rounded-full object-cover"
          />
          <div
            v-else
            class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-sm font-medium"
          >
            {{ getInitials(userDisplayName || 'U') }}
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ userDisplayName }}
            </p>
            <p class="text-xs text-gray-500 truncate">
              {{ user?.email }}
            </p>
          </div>
        </div>
      </div>

      <!-- Menu Items -->
      <div class="py-1">
        <button
          @click="handleSignOut"
          :disabled="loading"
          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
          <LogOut v-else class="w-4 h-4" />
          {{ loading ? 'Signing out...' : 'Sign out' }}
        </button>
      </div>
    </div>

    <!-- Backdrop to close dropdown -->
    <div
      v-if="isDropdownOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Button } from '@/components/ui/button'
import { LogOut, Loader2 } from 'lucide-vue-next'
import { useAuth } from '@/composables/useAuth'

// Auth composable
const { user, userDisplayName, userAvatar, signOut, loading } = useAuth()

// Local state
const isDropdownOpen = ref(false)

// Methods
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const handleSignOut = async () => {
  await signOut()
  closeDropdown()
}

// Close dropdown on escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
