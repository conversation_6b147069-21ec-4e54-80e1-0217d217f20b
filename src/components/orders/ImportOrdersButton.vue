<template>
    <!-- Sign In Dialog -->
    <SignInDialog v-if="!isAuthenticated" :open="showSignInDialog" @update:open="handleSignInDialogOpen"
        @success="handleSignInSuccess">
        <Button variant="outline">Get Started, It's Free!</Button>
    </SignInDialog>

    <!-- Import Orders Dialog (for authenticated users) -->
    <Dialog v-else :open="isOpen" @update:open="handleDialogOpen">
        <DialogTrigger as-child>
            <Button variant="outline">Get Started, It's Free!</Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
                <DialogTitle>
                    <span v-if="fileAdded">Importing Orders</span>
                    <span v-else>Import Orders</span>
                </DialogTitle>
                <DialogDescription>
                    <span v-if="fileAdded">We're doing all the work you didn't want to do manually</span>
                    <span v-else>Kindly import your csv file here</span>
                </DialogDescription>
            </DialogHeader>
            <template v-if="fileAdded">
                <Loader2 class="animate-spin mx-auto"></Loader2>
            </template>
            <div v-else class="space-y-4">
                <!-- Template Download Section -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-blue-900">Need a template?</h4>
                            <p class="text-xs text-blue-700 mt-1">Download our CSV template with sample data to get
                                started</p>
                        </div>
                        <Button @click="downloadTemplate" variant="outline" size="sm"
                            class="border-blue-300 text-blue-700 hover:bg-blue-100">
                            <Download class="w-4 h-4 mr-2" />
                            Download Template
                        </Button>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div @click="open()" ref="dropZoneRef" :class="{ 'border-green-200': isOverDropZone }"
                    class="cursor-pointer border-4 border-dashed border-spacing-10 h-[200px] rounded-2xl flex flex-col items-center justify-center gap-2 text-muted-foreground text-xs">
                    <File></File>
                    <p>Drop file</p>
                    <p>or</p>
                    <p class="text-green-400 cursor-pointer">Browse files</p>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useFileDialog, useDropZone } from '@vueuse/core'
import { File, Loader2, Download } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Dialog, DialogTitle, DialogDescription, DialogTrigger, DialogContent, DialogHeader } from '@/components/ui/dialog'
import { useLabelStore } from '@/stores/label'
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import SignInDialog from '@/components/auth/SignInDialog.vue'
import { useAuth } from '@/composables/useAuth'

// STORES
const labelStore = useLabelStore()

// AUTH
const { isAuthenticated } = useAuth()

// DIALOG
const isOpen = ref(false)
const fileAdded = ref(false)
const showSignInDialog = ref(false)

const handleDialogOpen = (state: boolean) => {
    isOpen.value = state
}

const handleSignInDialogOpen = (state: boolean) => {
    showSignInDialog.value = state
}

const handleSignInSuccess = () => {
    // After successful sign-in, open the import dialog
    isOpen.value = true
}

// FILE HANDLER
const { open, onChange } = useFileDialog({
    accept: '.csv',
    multiple: false
})

onChange((files: FileList | null) => {
    if (files) {
        labelStore.processAndUploadLabels([...files])
    }
})

// FILE DROP
const dropZoneRef = ref<HTMLDivElement>()



const onDrop = async (files: File[] | null) => {
    labelStore.processAndUploadLabels(files)
}

const { isOverDropZone } = useDropZone(dropZoneRef, {
    onDrop,
    dataTypes: ['text/csv']
})

const router = useRouter()

watch(() => labelStore.hasImportedLabels, (hasLabels) => {
    if (hasLabels) {
        router.push('/preview')
    }
})

// TEMPLATE DOWNLOAD
const downloadTemplate = () => {
    // Create sample data with the required fields
    const templateData = [
        {
            recipient_name: 'John Doe',
            recipient_address: '123 Main St, Anytown, ST 12345',
            recipient_phone: '******-123-4567',
            vendor_name: 'ABC Company',
            vendor_address: '456 Business Ave, Commerce City, ST 67890',
            vendor_phone: '******-987-6543',
            items: 'Widget A, Widget B, Premium Service'
        },
        {
            recipient_name: 'Jane Smith',
            recipient_address: '789 Oak Road, Somewhere, ST 54321',
            recipient_phone: '******-234-5678',
            vendor_name: 'XYZ Corporation',
            vendor_address: '321 Industry Blvd, Business Park, ST 09876',
            vendor_phone: '******-876-5432',
            items: 'Product X, Product Y'
        },
        {
            recipient_name: 'Bob Johnson',
            recipient_address: '456 Pine Street, Hometown, ST 98765',
            recipient_phone: '******-345-6789',
            vendor_name: 'DEF Enterprises',
            vendor_address: '654 Corporate Dr, Enterprise Zone, ST 56789',
            vendor_phone: '******-765-4321',
            items: 'Service Package, Additional Item'
        }
    ]

    // Convert to CSV format
    const headers = Object.keys(templateData[0])
    const csvContent = [
        headers.join(','),
        ...templateData.map(row =>
            headers.map(header => `"${row[header as keyof typeof row]}"`).join(',')
        )
    ].join('\n')

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'orders_template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
}


</script>