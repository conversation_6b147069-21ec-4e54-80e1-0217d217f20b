import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

export interface OpenGraphData {
  title?: string
  description?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
}

export interface TwitterCardData {
  card?: 'summary' | 'summary_large_image' | 'app' | 'player'
  title?: string
  description?: string
  image?: string
  imageAlt?: string
}

const defaultOGData: OpenGraphData = {
  title: 'Lavvel - Powerful Label Generation for Your Business',
  description: 'Powerful, self-serve product and label generation to take your business shipping to the next level. Generate professional shipping labels instantly and for free.',
  image: 'https://lavvel.com/og-image.png',
  url: 'https://lavvel.com/',
  type: 'website',
  siteName: 'Lavvel',
  locale: 'en_US'
}

const defaultTwitterData: TwitterCardData = {
  card: 'summary_large_image',
  title: 'Lavvel - Powerful Label Generation for Your Business',
  description: 'Powerful, self-serve product and label generation to take your business shipping to the next level. Generate professional shipping labels instantly and for free.',
  image: 'https://lavvel.com/og-image.png',
  imageAlt: 'Lavvel - Professional Label Generation Platform'
}

export function useOpenGraph() {
  const route = useRoute()
  const currentOGData = ref<OpenGraphData>({ ...defaultOGData })
  const currentTwitterData = ref<TwitterCardData>({ ...defaultTwitterData })

  // Helper function to update meta tag content
  const updateMetaTag = (property: string, content: string) => {
    let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement
    if (!meta) {
      meta = document.createElement('meta')
      meta.setAttribute('property', property)
      document.head.appendChild(meta)
    }
    meta.setAttribute('content', content)
  }

  // Helper function to update regular meta tag
  const updateMetaName = (name: string, content: string) => {
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement
    if (!meta) {
      meta = document.createElement('meta')
      meta.setAttribute('name', name)
      document.head.appendChild(meta)
    }
    meta.setAttribute('content', content)
  }

  // Update document title
  const updateTitle = (title: string) => {
    document.title = title
    updateMetaName('title', title)
  }

  // Update Open Graph tags
  const updateOpenGraph = (data: Partial<OpenGraphData>) => {
    const ogData = { ...currentOGData.value, ...data }
    currentOGData.value = ogData

    if (ogData.title) {
      updateTitle(ogData.title)
      updateMetaTag('og:title', ogData.title)
    }
    if (ogData.description) {
      updateMetaName('description', ogData.description)
      updateMetaTag('og:description', ogData.description)
    }
    if (ogData.image) {
      updateMetaTag('og:image', ogData.image)
    }
    if (ogData.url) {
      updateMetaTag('og:url', ogData.url)
      // Update canonical link
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
      if (!canonical) {
        canonical = document.createElement('link')
        canonical.setAttribute('rel', 'canonical')
        document.head.appendChild(canonical)
      }
      canonical.setAttribute('href', ogData.url)
    }
    if (ogData.type) {
      updateMetaTag('og:type', ogData.type)
    }
    if (ogData.siteName) {
      updateMetaTag('og:site_name', ogData.siteName)
    }
    if (ogData.locale) {
      updateMetaTag('og:locale', ogData.locale)
    }
  }

  // Update Twitter Card tags
  const updateTwitterCard = (data: Partial<TwitterCardData>) => {
    const twitterData = { ...currentTwitterData.value, ...data }
    currentTwitterData.value = twitterData

    if (twitterData.card) {
      updateMetaTag('twitter:card', twitterData.card)
    }
    if (twitterData.title) {
      updateMetaTag('twitter:title', twitterData.title)
    }
    if (twitterData.description) {
      updateMetaTag('twitter:description', twitterData.description)
    }
    if (twitterData.image) {
      updateMetaTag('twitter:image', twitterData.image)
    }
    if (twitterData.imageAlt) {
      updateMetaTag('twitter:image:alt', twitterData.imageAlt)
    }
  }

  // Set page-specific OG data
  const setPageOG = (ogData: Partial<OpenGraphData>, twitterData?: Partial<TwitterCardData>) => {
    updateOpenGraph(ogData)
    if (twitterData) {
      updateTwitterCard(twitterData)
    } else {
      // Sync Twitter data with OG data
      updateTwitterCard({
        title: ogData.title,
        description: ogData.description,
        image: ogData.image
      })
    }
  }

  // Reset to default OG data
  const resetToDefault = () => {
    updateOpenGraph(defaultOGData)
    updateTwitterCard(defaultTwitterData)
  }

  // Get current URL for dynamic OG URL
  const getCurrentUrl = () => {
    return window.location.origin + route.fullPath
  }

  // Watch route changes to update URL
  watch(() => route.fullPath, (newPath) => {
    const newUrl = window.location.origin + newPath
    updateOpenGraph({ url: newUrl })
    updateMetaTag('twitter:url', newUrl)
  })

  return {
    currentOGData,
    currentTwitterData,
    updateOpenGraph,
    updateTwitterCard,
    setPageOG,
    resetToDefault,
    getCurrentUrl
  }
}
