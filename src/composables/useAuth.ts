import { useAuthStore } from '@/stores/auth'
import { storeToRefs } from 'pinia'

export function useAuth() {
  const authStore = useAuthStore()
  
  // Reactive refs from store
  const { 
    user, 
    session, 
    loading, 
    error, 
    isAuthenticated, 
    userDisplayName, 
    userAvatar 
  } = storeToRefs(authStore)
  
  // Actions from store
  const { 
    signInWithGoogle, 
    signOut, 
    initialize, 
    clearError 
  } = authStore

  return {
    // State
    user,
    session,
    loading,
    error,
    
    // Computed
    isAuthenticated,
    userDisplayName,
    userAvatar,
    
    // Actions
    signInWithGoogle,
    signOut,
    initialize,
    clearError
  }
}
