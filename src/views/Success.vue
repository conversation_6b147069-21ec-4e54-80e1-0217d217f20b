<template>
    <div class="min-h-screen bg-gray-50">
        <!-- Navigation -->
        <div class="bg-white border-b border-gray-200">
            <div class="max-w-4xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <Button variant="ghost" @click="$router.push('/preview')"
                            class="text-gray-600 hover:text-gray-900">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            Back
                        </Button>
                        <div class="h-4 w-px bg-gray-300"></div>
                        <Button variant="ghost" @click="$router.push('/')" class="text-gray-600 hover:text-gray-900">
                            <Home class="mr-2 h-4 w-4" />
                            Home
                        </Button>
                    </div>

                    <Button variant="outline" @click="generateMore" class="text-sm">
                        <Plus class="mr-2 h-4 w-4" />
                        Generate More
                    </Button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto px-6 py-12">

            <!-- Success Header -->
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
                    <CheckCircle class="w-8 h-8 text-green-600" />
                </div>

                <h1 class="text-3xl font-semibold text-gray-900 mb-3">
                    Labels Generated Successfully
                </h1>

                <p class="text-gray-600" v-if="pdfInfo">
                    {{ pdfInfo.labelsCount }} {{ pdfInfo.labelsCount === 1 ? 'label' : 'labels' }} ready for download
                </p>
            </div>

            <!-- PDF Card -->
            <div class="bg-white rounded-xl border border-gray-200 shadow-sm mb-8" v-if="pdfInfo">
                <div class="p-8">
                    <!-- PDF Info -->
                    <div class="flex items-center justify-between mb-8">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-lg">
                                <FileText class="w-6 h-6 text-blue-600" />
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">{{ pdfInfo.filename }}</h3>
                                <p class="text-sm text-gray-500">{{ formatFileSize(pdfInfo.fileSize) }} • {{
                                    formatTimeAgo(pdfInfo.generatedAt) }}</p>
                            </div>
                        </div>

                        <div class="text-right">
                            <p class="text-sm text-gray-500">{{ formatDimensions(pdfInfo.dimensions) }}</p>
                            <p class="text-sm text-gray-500">{{ pdfInfo.labelsCount }} labels</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <Button @click="downloadPDF" class="flex-1 bg-gray-900 hover:bg-gray-800 text-white"
                            :disabled="isDownloading">
                            <Download class="mr-2 h-4 w-4" />
                            {{ isDownloading ? 'Downloading...' : 'Download PDF' }}
                        </Button>

                        <Button variant="outline" @click="openPrintPreview" class="flex-1 sm:flex-none">
                            <Printer class="mr-2 h-4 w-4" />
                            Print
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid sm:grid-cols-2 gap-4 mb-8">
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-lg">
                            <Plus class="w-4 h-4 text-blue-600" />
                        </div>
                        <h3 class="font-medium text-gray-900">Generate More</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Create additional labels with new data</p>
                    <Button variant="outline" @click="generateMore" class="w-full text-sm">
                        Start New Generation
                    </Button>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="flex items-center justify-center w-8 h-8 bg-green-50 rounded-lg">
                            <MessageCircle class="w-4 h-4 text-green-600" />
                        </div>
                        <h3 class="font-medium text-gray-900">Share Feedback</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Let us know about your experience</p>
                    <Button variant="outline" @click="shareFeedback" class="w-full text-sm">
                        Send Feedback
                    </Button>
                </div>
            </div>

            <!-- Tips -->
            <div class="bg-blue-50 rounded-lg border border-blue-200 p-6">
                <h3 class="font-medium text-gray-900 mb-4">Tips for better results</h3>
                <div class="space-y-2 text-sm text-gray-700">
                    <div class="flex items-start space-x-2">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <p>Use our CSV template for consistent formatting</p>
                    </div>
                    <div class="flex items-start space-x-2">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <p>Check printer settings before printing labels</p>
                    </div>
                    <div class="flex items-start space-x-2">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <p>Keep consistent dimensions for label batches</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '@/components/ui/button'
import {
    ArrowLeft, Download, FileText, Printer, Home, Plus, CheckCircle, MessageCircle
} from 'lucide-vue-next'
import { useLabelStore } from '@/stores/label'
import { useRouter } from 'vue-router'
import { shareSuccess } from '@/services/feedbackService'

const labelStore = useLabelStore()
const router = useRouter()
const isDownloading = ref(false)

// Get PDF info from store
const pdfInfo = computed(() => labelStore.lastGeneratedPDF)

// Redirect if no PDF was generated
onMounted(() => {
    if (!pdfInfo.value) {
        console.warn('No PDF found, redirecting to home')
        router.push('/')
    }
})

/**
 * Download the PDF again
 */
const downloadPDF = async (): Promise<void> => {
    if (!pdfInfo.value) {
        console.error('No PDF available for download')
        return
    }

    isDownloading.value = true
    try {
        labelStore.downloadLastPDF()
        console.log('PDF download initiated')
    } catch (error) {
        console.error('Failed to download PDF:', error)
    } finally {
        // Reset downloading state after a delay
        setTimeout(() => {
            isDownloading.value = false
        }, 2000)
    }
}

/**
 * Open print preview
 */
const openPrintPreview = (): void => {
    if (!pdfInfo.value) {
        console.error('No PDF available for print preview')
        return
    }

    // Create a blob URL and open in new window for printing
    const url = URL.createObjectURL(pdfInfo.value.blob)
    const printWindow = window.open(url, '_blank')

    if (printWindow) {
        printWindow.onload = () => {
            printWindow.print()
            // Clean up the URL after printing
            setTimeout(() => {
                URL.revokeObjectURL(url)
            }, 1000)
        }
    } else {
        console.error('Failed to open print preview window')
    }
}

/**
 * Navigate to generate more labels
 */
const generateMore = (): void => {
    router.push('/')
}

/**
 * Share feedback about success
 */
const shareFeedback = (): void => {
    shareSuccess()
}



/**
 * Format file size in human readable format
 */
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format dimensions for display
 */
const formatDimensions = (dimensions: any): string => {
    return `${dimensions.width} × ${dimensions.height} ${dimensions.unit}`
}

/**
 * Format time for display
 */
const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

/**
 * Format time ago
 */
const formatTimeAgo = (date: Date): string => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
        return 'just now'
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else {
        return date.toLocaleDateString()
    }
}
</script>

<style scoped>
/* Simple, clean styles for modern success page */
</style>