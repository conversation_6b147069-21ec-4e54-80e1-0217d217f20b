import './assets/index.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import posthogPlugin from "./plugins/posthog";

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(posthogPlugin); //install the plugin

// Initialize auth after mounting
app.mount('#app')

// Initialize authentication
import { useAuthStore } from '@/stores/auth'
const authStore = useAuthStore()
authStore.initialize()
