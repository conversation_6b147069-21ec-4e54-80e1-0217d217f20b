import { ref, computed, readonly } from 'vue'
import { defineStore } from 'pinia'
import { supabase, type User, type AuthSession } from '@/lib/supabase'
import type { AuthError, Session } from '@supabase/supabase-js'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const session = ref<Session | null>(null)
  const loading = ref(true)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const userDisplayName = computed(() => {
    if (!user.value) return null
    return user.value.user_metadata?.full_name ||
      user.value.user_metadata?.name ||
      user.value.email?.split('@')[0] ||
      'User'
  })
  const userAvatar = computed(() => {
    if (!user.value) return null
    return user.value.user_metadata?.avatar_url || user.value.user_metadata?.picture
  })

  // Actions
  const signInWithGoogle = async () => {
    try {
      loading.value = true
      error.value = null

      const { data, error: authError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin
        }
      })

      if (authError) {
        throw authError
      }

      return { data, error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to sign in with Google'
      error.value = errorMessage
      console.error('Google sign-in error:', err)
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  const signOut = async () => {
    try {
      loading.value = true
      error.value = null

      const { error: authError } = await supabase.auth.signOut()

      if (authError) {
        throw authError
      }

      user.value = null
      session.value = null

      return { error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to sign out'
      error.value = errorMessage
      console.error('Sign out error:', err)
      return { error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  const initialize = async () => {
    try {
      loading.value = true

      // Get initial session
      const { data: { session: initialSession }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        throw sessionError
      }

      if (initialSession) {
        session.value = initialSession
        user.value = initialSession.user as User
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange((event, newSession) => {
        console.log('Auth state changed:', event, newSession)

        session.value = newSession
        user.value = newSession?.user as User || null

        if (event === 'SIGNED_OUT') {
          user.value = null
          session.value = null
        }
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize auth'
      error.value = errorMessage
      console.error('Auth initialization error:', err)
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user: readonly(user),
    session: readonly(session),
    loading: readonly(loading),
    error: readonly(error),

    // Computed
    isAuthenticated,
    userDisplayName,
    userAvatar,

    // Actions
    signInWithGoogle,
    signOut,
    initialize,
    clearError
  }
})
