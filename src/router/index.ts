import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
      meta: {
        title: 'Lavvel - Powerful Label Generation for Your Business',
        description: 'Powerful, self-serve product and label generation to take your business shipping to the next level. Generate professional shipping labels instantly and for free.',
        ogImage: '/og-image.png'
      }
    },
    {
      path: '/preview',
      name: 'Preview',
      component: () => import('@/views/Preview.vue'),
      meta: {
        title: 'Preview Your Labels - Lavvel',
        description: 'Preview and customize your shipping labels before downloading. Make sure everything looks perfect for your business needs.',
        ogImage: '/og-image-preview.png'
      }
    },
    {
      path: '/success',
      name: 'Success',
      component: () => import('@/views/Success.vue'),
      meta: {
        title: 'Labels Generated Successfully - Lavvel',
        description: 'Your shipping labels have been generated successfully! Download your professional labels and streamline your shipping process.',
        ogImage: '/og-image-success.png'
      }
    },
    {
      path: '/index.html',
      redirect: '/'
    }
  ]
})

// Global navigation guard to update OG tags
router.beforeEach((to, from, next) => {
  // This will be handled by the useOpenGraph composable in each component
  next()
})

export default router
